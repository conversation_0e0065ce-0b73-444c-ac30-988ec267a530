This is a bit convoluted, but you run it (so far) this way:

You have to compile Py<PERSON><PERSON> with strange options. I'll explain them below.
In the pypy checkout, run:

  ./rpython/bin/rpython -Ojit --shared --gcrootfinder=shadowstack pypy/goal/targetpypystandalone

Options:

  -Ojit - standard PyPy build with JIT
  --shared - as expected, creates a .so
  --gcrootfinder - on linux, the default rootfinder is asmgcc which has trouble
                   with position independent code. This option is not needed
                   on OS X or windows.

If for some strange/dangerous/wrong reason your uWSGI binary is linked with cpython you could have name collisions
with pypy cpyext. In such crazy case you can remove cpyext support with:

  ./rpython/bin/rpython -Ojit --shared --gcrootfinder=shadowstack pypy/goal/targetpypystandalone --withoutmod-cpyext

By default the pypy uWSGI plugin will load the libpypy-c.so library. You should have it in the LD_LIBRARY_PATH, otherwise you can set its path
with --pypy-lib option (pass an absolute path to it)

You have to specify the pypy home too, you can use the --pypy-home option or the PYPY_HOME var

   PYPY_HOME=/home/<USER>/pypy/lib_pypy uwsgi --pypy-lib /opt/pypy/libpypy-c.so --http-socket :9090 --pypy-wsgi werkzeug.testapp:test_app


The funny part about the uWSGI pypy plugin is that it is mainly written in python. The pypy_setup.py script is linked to the plugin, but you can override
it with the --pypy-setup option
