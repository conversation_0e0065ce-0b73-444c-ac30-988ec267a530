
worker_processes  1;

events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;

    keepalive_timeout  65;

    gzip  on;

    upstream uwsgi_server {
	server 127.0.0.1:3031;
    }

    include uwsgi_params;	
    uwsgi_param UWSGI_FILE    $app;
    uwsgi_param UWSGI_PYHOME    $virtualenv;
    

    server {
	listen 8026;
	server_name	flask001;
	set $app vhosttest/flask001/app1.py;
	set $virtualenv vhosttest/venv001;
	location / {
		uwsgi_pass uwsgi_server;
	}
    }

    server {
	listen 8026;
	server_name	flask002;
	set $app vhosttest/flask002/app2.py;
	set $virtualenv vhosttest/venv001;
	location / {
		uwsgi_pass uwsgi_server;
	}
    }


}
