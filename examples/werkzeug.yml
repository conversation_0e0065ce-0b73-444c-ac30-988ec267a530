# werkzeug test module via YAML
uwsgi:
  module: werkzeug.testapp:test_app # a comment
  socket: :3032 # another comment
# a line comment
# another line comment

# adding a tab as separator (discouraged)
  master:	1
  processes: 8 # the number of processes
  memory-report: 1
  show-config: true

# now a psgi app via plugin
app2:
  plugins: psgi
  socket: :3032 # another comment
# adding a tab as separator (discouraged)
  master:	1
  processes: 8 # the number of processes
  memory-report: 1
  psgi: test.psgi
  show-config: true
