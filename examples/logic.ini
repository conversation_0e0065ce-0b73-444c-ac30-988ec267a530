[uwsgi]

for = 4031 14032 4033 14034 24035
  socket = /tmp/%(_)/uwsgi.sock
  socket = 192.*:%(_)
  print = ciao %(_) hello %(_) bye %(_)
endfor =


socket = :4045
socket = :4046
socket = :4047
socket = :4048


socket = :4049
socket = :4050
socket = :4051

show-config =

if-env = PIPPO
  print =
  print = PIPPO e' definita e il suo valore e' %(_) boh
endif =

socket = :5051
socket = :5052
socket = :5053
socket = :5054

if-exists = /etc/services
  print = il file %(_) esiste $(PATH)
endif =

if-exists = /etc/foobar
  print = il file %(_) esiste $(PATH)
endif =

socket = :6060
if-file = /etc/fstab
  socket = :7171
endif =

ifdir = /tmp
  print = %(_) is a directory
endif=
