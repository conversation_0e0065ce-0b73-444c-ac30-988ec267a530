[uwsgi]
plugin = 0:notfound, router_xmldir, xslt
http-socket = :8080

docroot = .
check-static = %(docroot)

route-if = isdir:%(docroot)/${PATH_INFO} goto:index
route-run = last:

route-label = index
; uncomment to link CSS stylesheet /css/default.css
;route-run = toxslt:stylesheet=examples/xmlindex-html.xsl,params=stylesheet=/css/default.css,content_type=text/html
; use embedded CSS
route-run = toxslt:stylesheet=examples/xmlindex-html.xsl,params=stylesheet=,content_type=text/html
route-run = xmldir:%(docroot)/${PATH_INFO}

