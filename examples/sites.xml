<uwsgi>
	
	<socket>:3031</socket>
	<master/>
	<processes>4</processes>
	<memory-debug/>
	
	<vhost-host/>

	<app domain="sirius.local">
		<script>werkzeug.testapp:test_app</script>
	</app>

	<env>TRAC_ENV=/Users/<USER>/uapps/utrac</env>
	<app domain="*************:8080">
		<script>trac.web.main:dispatch_request</script>
	</app>

	<app domain="*************:8080" mountpoint="/sinatra">
		<rack>/Users/<USER>/uwsgi/config.ru</rack>
	</app>
</uwsgi>
