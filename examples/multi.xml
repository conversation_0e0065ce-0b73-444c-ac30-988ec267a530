<uwsgi>

	<!--
	
		DO NOT USE <app> TAG !!!

		It is deprecated and will be removed from 1.2

	-->

	<plugins>psgi</plugins>
	<plugins>rack</plugins>

	<http>:9090</http>

	<socket>:3031</socket>
	<socket>127.0.0.1:3032</socket>

	<manage-script-name/>
	
	<master/>
	<processes>8</processes>

<!--
	<vhost/>
	<vhost-host/>
-->

	<env>TRAC_ENV=utrac</env>


	<app mountpoint="/">
		<module>werkzeug.testapp:test_app</module>
	</app>

	<app mountpoint="/foobar">werkzeug.testapp:test_app</app>

	<app mountpoint="/trac">
		<module>trac.web.main:dispatch_request</module>
	</app>

	<app mountpoint="/trac/foo">
		<module>werkzeug.testapp:test_app</module>
	</app>

	<!-- eval is broken -->
	<app mountpoint="/bzr">
		<eval>
from bzrlib.transport.http import wsgi
application = wsgi.make_app("mybzr", "/bzr", readonly=False)
		</eval>
	</app>

	<app domain="sirius002:9090">
		<module>trac.web.main:dispatch_request</module>
	</app>

	<app domain="sirius003:9090">
		<rack>config2.ru</rack>
	</app>

	<app mountpoint="precise64.local">
		<psgi>test.psgi</psgi>
	</app>

</uwsgi>
