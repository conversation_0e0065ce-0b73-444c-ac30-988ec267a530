[uwsgi]

master = true
processes = 4
threads = 8

; initialize 4 sockets with 4 different protocols
socket = :3031
socket = :3032
socket = :3033
socket = :3034

socket-protocol = 0,uwsgi
socket-protocol = 1,http
socket-protocol = 2,fastcgi
socket-protocol = 3,uwsgidump


; add a cache with 1000 items of 96k
cache = 1000
cache-blocksize = 98304


log-micros = true

module = heavytest
mount = /cc=uwsgicc/__init__.py
