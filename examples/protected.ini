[uwsgi]

http-socket = :9090

route = ^/foo basicauth:foobar,kratos:

; a simple user:passowrd mapping
route = .* basicauth:nether realm 3,serena:alessandro
; htpasswd-like real-time parsing
route = .* basicauth:nether realm 2,.htpasswd
; user-only auth
route = .* basicauth:nether realm 4,roberta:
route = .* basicauth:nether realm 4,

route = ^/bar basicauth:foobar,kratos:



module = welcome
processes = 4
master = true
