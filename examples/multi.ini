[uwsgi]

; socket 0
socket = :3033
; socket 1
socket = :3034
; socket 2
socket = pippo1.sock
; socket 3
socket = pippo2.sock
; socket 4
socket = 127.0.0.1:1717

map-socket = 0:1,2,3,4
map-socket = 1:4
map-socket = 2:10
map-socket = 3:5,6,9
map-socket = 4:7,8,9


vacuum = true
memory-report = true
master = true
processes = 10
;mount = /app1=werkzeug.testapp:test_app
;mount = /app2=config:/Users/<USER>/uwsgi/my.ini
;mount = /app3=pinaxsite/deploy/pinax.wsgi
