[uwsgi]
xml = libxml2
yaml = libyaml
json = true
ssl = true
pcre = true
routing = true
debug = false
unbit = false
malloc_implementation = libc
extras =
plugins =
bin_name = /usr/lib/libuwsgi.so
append_version = ubuntu
plugin_dir = /usr/lib/uwsgi
embedded_plugins =
as_shared_library = true

locking = auto
event = auto
timer = auto
filemonitor = auto

blacklist =
whitelist =

embed_files =

embed_config =
