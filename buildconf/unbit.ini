[uwsgi]
xml = expat
yaml = embedded
json = false
ssl = true
pcre = true
routing = true
debug = false
unbit = true
malloc_implementation = libc
extras =
plugins =
bin_name = /opt/unbit/uwsgi19/lib/libuwsgi.so
append_version = unbit
plugin_dir = /opt/unbit/uwsgi19/plugins
embedded_plugins =
as_shared_library = true

locking = auto
event = auto
timer = auto
filemonitor = auto

blacklist =
whitelist =

embed_files =

embed_config =
