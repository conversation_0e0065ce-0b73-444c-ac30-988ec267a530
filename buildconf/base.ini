[uwsgi]
xml = auto
yaml = true
json = auto
ssl = auto
pcre = auto
routing = auto
debug = false
unbit = false
malloc_implementation = libc
extras =
plugins =
bin_name = uwsgi
append_version =
plugin_dir = .
embedded_plugins = %(main_plugin)s, ping, cache, nagios, rrdtool, carbon, rpc, corerouter, fastrouter, http, ugreen, signal, syslog, rsyslog, logsocket, router_uwsgi, router_redirect, router_basicauth, zergpool, redislog, mongodblog, router_rewrite, router_http, logfile, router_cache, rawrouter, router_static, sslrouter, spooler, cheaper_busyness, symcall, transformation_tofile, transformation_gzip, transformation_chunked, transformation_offload, router_memcached, router_redis, router_hash, router_expires, router_metrics, transformation_template, stats_pusher_socket, router_fcgi
as_shared_library = false

locking = auto
event = auto
timer = auto
filemonitor = auto

blacklist =
whitelist =

embed_files =

embed_config =
