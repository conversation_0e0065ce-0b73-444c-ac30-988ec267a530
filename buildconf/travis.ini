[uwsgi]
main_plugin = psgi,rack,lua,python,gevent,php,cgi,pty,xslt,msgpack,geoip,pam,ldap,mono,jvm,ring,jwsgi,servlet,pypy,airbrake,alarm_curl,asyncio,cheaper_backlog2,clock_monotonic,clock_realtime,cplusplus,curl_cron,dumbloop,dummy,echo,emperor_amqp,emperor_pg,emperor_zeromq,example,exception_log,fiber,forkptyrouter,graylog2,legion_cache_fetch,libffi,logcrypto,logpipe,logzmq,matheval,notfound,rbthreads,router_access,router_radius,router_spnego,router_xmldir,sqlite3,ssi,stats_pusher_file,stats_pusher_statsd,tornado,transformation_toupper,tuntap,webdav,xattr,zabbix
inherit = base
