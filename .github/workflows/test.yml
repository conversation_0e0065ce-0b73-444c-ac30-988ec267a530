name: Test

on:
  push:
    branches: [ master, uwsgi-2.0 ]
  pull_request:
    branches: [ master, uwsgi-2.0 ]

jobs:

  unittest:
    runs-on: ubuntu-22.04
    steps:
    - name: Install dependencies
      run: |
        sudo apt update -qq
        sudo apt install --no-install-recommends -qqyf \
          libpcre3-dev lib<PERSON>sson-dev libcap2-dev \
          check
    - uses: actions/checkout@v4
    - name: Run unit tests
      run: make unittests

  test:
    runs-on: ubuntu-22.04
    steps:
    - name: Install dependencies
      run: |
        sudo apt update -qq
        sudo apt install --no-install-recommends -qqyf \
          libpcre2-dev libjansson-dev libcap2-dev \
          php-dev libphp-embed libargon2-dev libsodium-dev \
          pypy3 default-jdk-headless libperl-dev \
          ruby-dev ruby-rack
    - uses: actions/checkout@v4
    - name: Set env
      run: echo "PROFILE=integration-tests" >> $GITHUB_ENV
    - name: Run integration tests
      run: make all tests

  python:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        python-version: ["2.7", "3.7", "3.8", "3.9", "3.10", "3.11", "3.12", "3.13"]
        test-suite: [python, deadlocks]
    steps:
    - name: Add deadnakes ppa
      run: sudo add-apt-repository ppa:deadsnakes/ppa -y
    - name: Install dependencies
      run: |
        sudo apt update -qq
        sudo apt install --no-install-recommends -qqyf python${{ matrix.python-version }}-dev \
          libpcre2-dev libjansson-dev libcap2-dev \
          curl
    - name: Install distutils
      if: contains(fromJson('["3.7","3.8","3.9","3.10","3.11"]'), matrix.python-version)
      run: |
        sudo apt install --no-install-recommends -qqyf python${{ matrix.python-version }}-distutils \
    - uses: actions/checkout@v4
    - name: Build uWSGI binary
      run: make
    - name: Build python${{ matrix.python-version }} plugin
      run: |
        PYTHON_VERSION=${{ matrix.python-version }}
        PYTHON_VERSION=python${PYTHON_VERSION//.}
        /usr/bin/python${{ matrix.python-version }} -V
        /usr/bin/python${{ matrix.python-version }} uwsgiconfig.py --plugin plugins/python base $PYTHON_VERSION
    - name: run smoke tests
      run: |
        PYTHON_VERSION=${{ matrix.python-version }}
        PYTHON_VERSION=python${PYTHON_VERSION//.}
        ./tests/gh-${{ matrix.test-suite }}.sh ${PYTHON_VERSION}

  rack:
    runs-on: ubuntu-22.04
    strategy:
      matrix:
        rack-version: ["300"]
    steps:
    - name: Install dependencies
      run: |
        sudo apt update -qq
        sudo apt install --no-install-recommends -qqyf python3-dev \
          libpcre2-dev libjansson-dev libcap2-dev ruby3.0-dev \
          curl
    - uses: actions/checkout@v4
    - name: Build uWSGI binary
      run: make
    - name: Build rack plugin
      run: |
        ruby -v
        UWSGICONFIG_RUBYPATH=ruby /usr/bin/python uwsgiconfig.py --plugin plugins/rack base rack${{ matrix.rack-version }}
    - name: run smoke tests
      run: |
        ./tests/gh-rack.sh rack${{ matrix.rack-version}}
