main = Server(
    uuid="f400bf85-4538-4f7a-8908-67e313d515c2",
    access_log="/logs/access.log",
    error_log="/logs/error.log",
    chroot="./",
    default_host="**************",
    name="test",
    pid_file="/run/mongrel2.pid",
    port=6767,
    hosts = [
        Host(name="**************", routes={
            '/': Handler(send_spec='tcp://**************:9999',
                    send_ident='54c6755b-9628-40a4-9a2d-cc82a816345e', 
                    recv_spec='tcp://**************:9998', recv_ident='')
        })
    ]
)

settings = {'upload.temp_store':'tmp/mongrel2.upload.XXXXXX'}
servers = [main]


uwsgi = { 'socket':':3031', 'master':'true'}
