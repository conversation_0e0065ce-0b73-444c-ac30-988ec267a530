#!/bin/bash

pydir=${1:-/usr}


gensup() {
    for SUP in Cond Free Leak Overlap Addr1 Addr2 Addr4 Addr8 Addr16 Value1 Value2 Value4 Value8 Value16 ; do
        echo "
{
    Autogenerated $1 suppression
    Memcheck:${SUP}
    obj:$2
}
"
    done
}


while read SO ; do
    gensup libpython "$SO"
done < <(find ${pydir}/lib*/ -type f -name libpython*)


while read SO ; do
    gensup python "$SO"
done < <(find ${pydir}/lib*/python*/ -type f -name \*.so)

