[uwsgi]
http-socket = :9090
module = werkzeug.testapp:test_app

; allows up to 100 authenticated users
cache = 100
master = true

; check if a uwsgiauth cookie is available
route-if-not = empty:${cookie[uwsgiauth]} goto:cookieauth

; 2 users
route-run = basicauth-next:Test0001,uno:due
route-run = basicauth:Test0001,test:foo

; create an uuid for authorization and place it in AUTH_UUID request var
route-run = addvar:AUTH_UUID=${uwsgi[uuid]}
; place it in the cache as REMOTE_USER:REMOTE_ADDR for 1 hour
route-run = cacheset:key=${AUTH_UUID},value=${REMOTE_USER}:${REMOTE_ADDR},expires=3600
; set the auth cookie
route-run = addheader:Set-Cookie: uwsgiauth=${AUTH_UUID}; Expires=${httptime[3600]}
; continue to the request plugin handler
route-run = last:

; here we manage the cookie (with some logging for debug)
route-label = cookieauth
route-run = log:COOKIE FOUND
; set AUTH_UUID with the cookie value
route-run = addvar:AUTH_UUID=${cookie[uwsgiauth]}
route-run = log:AUTH_UUID=${AUTH_UUID}
; get the cache value and place it in the var
route-run = cachevar:key=${AUTH_UUID},var=AUTH_VALUE
route-run = log:AUTH_VALUE=${AUTH_VALUE}
; if the item is not found reset credentials
route-if = empty:${AUTH_VALUE} goto:reset
; check if the cookie is valid for the remote address
route-if-not = endswith:${AUTH_VALUE};:${REMOTE_ADDR} goto:reset
route-run = log:OK
; set REMOTE_USER (the parser stops at the first colon)
route-run = setuser:${AUTH_VALUE}
; set the auth cookie (could be useless when using some framework)
route-run = addheader:Set-Cookie: uwsgiauth=${AUTH_UUID}
route-run = last:

; reset credentials
route-label = reset
route-run = log:RESETTING CREDENTIALS
route-run = addheader:Set-Cookie: uwsgiauth=invalid; expires=Thu, 01 Jan 1970 00:00:00 GMT
; force a 401
route-run = basicauth:Test0001,
