[uwsgi]
http-socket = :9090

; 8 bytes blocksize is the right thing to do
cache2 = name=limiter,items=1000,blocksize=8
; allows expiring items
master = true

; get the cache value for the current ip (if any)
route-run = cachevar:key=${REMOTE_ADDR},as_num=1,var=LIMIT
; block the peer if it did more than 10 requests in the last 30 seconds
route-if = >:${LIMIT};10 goto:blockit
; increment the value (expires value will not be updated)
route-run = cacheinc:key=${REMOTE_ADDR},expires=30
; end of the checks
route-run = last:

module = werkzeug.testapp:test_app
threads = 10

; block the client
route-label = blockit
route-run = log:STOP YOU BASTARD !!!
route-run = break:503
