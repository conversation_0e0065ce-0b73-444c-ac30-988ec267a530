[uwsgi]
plugins = geoip, router_cache
http-socket = :9090

; 8 bytes blocksize is the right thing to do
cache2 = name=limiter,items=1000,blocksize=8
; allows expiring items
master = true

; get the cache value for the current ip (if any)
route-run = cachevar:key=${REMOTE_ADDR},as_num=1,var=LIMIT
; block the peer if it did more than 10 404 requests in the last 30 seconds
route-if = >:${LIMIT};10 goto:blockit
; end of routing
route-run = last:


module = werkzeug.testapp:test_app
threads = 10

; track the user if a 404 is returned
final-route-if = equal:${uwsgi[status]};404 goto:trackit
; end of final routing
final-route-run = last:

final-route-label = trackit
; increase the value in the 'limiter' cache
final-route-run = cacheinc:key=${REMOTE_ADDR},expires=30
; increase the value if the connection is from italy !!!
final-route-if = equal:${geoip[country_name]};Italy cacheinc:key=${REMOTE_ADDR},expires=30

; block the client
route-label = blockit
route-run = log:STOP YOU BASTARD !!!
route-run = break:503
