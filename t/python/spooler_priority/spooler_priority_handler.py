#! /usr/bin/env python
# coding = utf-8

from __future__ import print_function
from spooler_priority_constants import tasks, LOGFILE
from os import remove
import uwsgi

counter = 0


def spoolerHandler(env):
    global counter
    # Spooler is handling a task
    with open(LOG<PERSON>LE, "a") as log:
        print("%s" % (env['name']), file=log)

    counter += 1

    if counter == len(tasks):
        # Each task has been processed.
        uwsgi.signal(17)

    # <PERSON>pool<PERSON> has done handling the task
    return uwsgi.SPOOL_OK

uwsgi.spooler = spoolerHandler

# Clear the logfile
try:
    remove(LOGFILE)
except OSError, e:  # log does not exist
    print(e)
