[uwsgi]
socket = /tmp/foo

cache2 = name=items_1,blocks=4,items=2,bitmap=1,blocksize=1
cache2 = name=items_2,blocks=4,items=3,bitmap=1,blocksize=1
cache2 = name=items_3,blocks=4,items=4,bitmap=1,blocksize=1
cache2 = name=items_4,blocks=5,items=5,bitmap=1,blocksize=1
cache2 = name=items_17,blocks=17,items=17,bitmap=1,blocksize=1
cache2 = name=items_4_10,blocks=5,items=5,bitmap=1,blocksize=10
cache2 = name=items_1_100000,blocks=1000,items=2,bitmap=1,blocksize=100
cache2 = name=items_non_bitmap,items=2,blocksize=20
cache2 = name=items_lru,items=3,blocksize=20,purge_lru=1
pyrun = t/cachebitmap.py
