#! uwsgi -S addressbook=./carddav carddav.ini
[uwsgi]
plugin = webdav

http-socket = :9090
http-socket-modifier1 = 35

route-run = basicauth:CardDav uWSGI server,unbit:unbit

webdav-add-option = addressbook
webdav-principal-base = /principals/users/

webdav-add-prop-href = urn:ietf:params:xml:ns:carddav addressbook-home-set /principals/users/unbit
webdav-add-rtype-collection-prop = urn:ietf:params:xml:ns:carddav addressbook
webdav-mount = %(addressbook)

